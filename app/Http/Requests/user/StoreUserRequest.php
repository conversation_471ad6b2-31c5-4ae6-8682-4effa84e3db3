<?php

namespace App\Http\Requests\user;

use App\Rules\MobileNumberValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class StoreUserRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    protected $stopOnFirstFailure = true;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'mobile' => ['required', 'numeric', 'gt:0', 'digits_between:8,13', 'unique:admins,mobile', new MobileNumberValidationRule()],
            'name' => 'required|max:50',
            'name_np' => 'nullable|max:50',
            'email' => 'required|max:50|unique:admins,email',
            'username' => 'required|max:50|alpha_num|unique:admins,username',
            'address' => 'nullable|max:50',
            'gender' => 'nullable|max:50|in:Male,Female,Other',
            'status' => 'boolean|max:50|boolean',
            'role' => 'required|exists:roles,id',
            'sectionIds' => 'nullable|array',
            'sectionIds.*' => 'exists:sections,id',
            'courseIds' => 'nullable|array',
            'courseIds.*' => 'exists:courses,id',
            'has_package_access' => 'required|boolean',
            'is_field_agent' => 'required|in:0,1',
            'state_id' => 'nullable',
            'district_id' => 'nullable',
            'city_id' => 'nullable',
            'street_address' => 'nullable',
            'ward_number' => 'nullable',
            'is_school_college_admin' => ['nullable', 'boolean'],
            'institute_id' => ['nullable', 'exists:collages,id', 'unique:admin_colleges,id']
        ];
    }

    public function messages()
    {
        return [
            'gender.in' => 'Gender must either Male or female or Other',

        ];
    }

    protected function passedValidation()
    {
        $this->merge([
            'admin_id' => request()->user()->id,
        ]);
    }
}
