<?php

namespace App\Http\Requests\user;

use App\Rules\CheckCurrentAndOldPasswordRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class UpdatePasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    protected $stopOnFirstFailure = true;

    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'password' => [
                'required', 'max:50',
                'confirmed',
                'different:old_password',
                Password::min(8),

            ],
            'old_password' => ['required', new CheckCurrentAndOldPasswordRule],
        ];
    }

    public function messages()
    {
        return [
            'password.confirmed' => 'New password and confirmed password mismatch',
            'password.min' => 'Password should be atleast 8 character',
        ];
    }

    public function attributes()
    {
        return [
            'password' => 'New Password',
        ];
    }

    protected function passedValidation()
    {
        $this->merge(
            ['password' => bcrypt($this->input('password'))]
        );
    }
}
