<?php

namespace App\Http\Requests\user;

use App\Models\Admin;
use App\Models\User;
use App\Rules\MobileNumberValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class UpdateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    protected $stopOnFirstFailure = true;

    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $user = Admin::findOrFail($this->route()->parameter('user'))->id;

        return [
            'mobile' => ['required', 'numeric', 'gt:0', 'digits_between:8,13', 'unique:admins,mobile,'.$user, new MobileNumberValidationRule()],
            'name' => 'required|max:50',
            'name_np' => 'nullable|max:50',
            'email' => 'required|max:50|unique:admins,email,'.$user,
            'username' => 'required|max:50|alpha_num|unique:admins,username,'.$user,
            'address' => 'nullable|max:50',
            'gender' => 'nullable|max:50|in:Male,Female,Other',
            'status' => 'boolean|max:50|boolean',
            'role' => 'required|exists:roles,id',
            'has_package_access' => 'required|boolean',
            'is_field_agent' => 'required|in:0,1',
            'state_id' => 'nullable',
            'district_id' => 'nullable',
            'city_id' => 'nullable',
            'street_address' => 'nullable',
            'ward_number' => 'nullable',
        ];
    }

    protected function passedValidation()
    {
        if (request()->has('password')) {
            $this->merge(
                ['password' => bcrypt($this->input('password'))]
            );
        }
    }
}
