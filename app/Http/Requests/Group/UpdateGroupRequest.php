<?php

namespace App\Http\Requests\Group;

use Illuminate\Foundation\Http\FormRequest;

class UpdateGroupRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $this->merge([
            'admin_id' => auth('admin-api')->id(),
        ]);

        return [
            'name' => 'required|max:255|unique:groups,name,'.$this->id.',id,deleted_at,NULL',
            'status' => 'required|in:0,1',
            'customer_ids' => 'nullable|array|exists:customers,id',
        ];
    }
}
