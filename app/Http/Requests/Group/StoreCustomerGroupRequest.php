<?php

namespace App\Http\Requests\Group;

use Illuminate\Foundation\Http\FormRequest;

class StoreCustomerGroupRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $this->merge([
            'admin_id' => auth('admin-api')->id(),
        ]);

        return [
            'group_id' => ['required', 'exists:groups,id'],
            'customer_ids' => ['required', 'array'],
            'customer_ids.*' => ['exists:customers,id'],
        ];
    }
}
