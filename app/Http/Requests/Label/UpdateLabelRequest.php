<?php

namespace App\Http\Requests\Label;

use Illuminate\Foundation\Http\FormRequest;

class UpdateLabelRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [

            'title' => 'required|string|max:255',
            'color' => 'required|string|max:255',
            'for_transfer' => 'required|numeric',
            'status' => 'nullable',
        ];
    }
}
