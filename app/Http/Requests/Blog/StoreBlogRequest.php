<?php

namespace App\Http\Requests\Blog;

use Illuminate\Foundation\Http\FormRequest;

class StoreBlogRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'title' => 'required|max:255',
            'description' => 'required',
            'status' => 'boolean',
            'featured' => 'boolean',
            'meta_title' => 'nullable|max:255',
            'meta_keywords' => 'nullable|max:255',
            'meta_description' => 'nullable|max:255',
            'extras' => 'nullable',
            'feature' => 'nullable|exists:media,id',
            'thumbnail' => 'nullable|exists:media,id',
            'blog_category_id' => 'required|exists:blog_categories,id',
            'tags' => 'nullable|array',
            'tags.*' => 'max:255',
        ];
    }

    protected function passedValidation()
    {
        $this->merge([
            'admin_id' => request()->user()->id,
        ]);
    }

    public function messages()
    {
        return [
            'tags.*.max' => 'Some of your tag exceeds more than 250 character ',
        ];
    }
}
