<?php

namespace App\Http\Requests\Call;

use Illuminate\Foundation\Http\FormRequest;

class CallSetupStoreRequest extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }


    public function rules(): array
    {
        return [
            'name' => 'required',
            'type' => 'required',
            'duration' => 'required',
            'break' => 'required',
            'extras' => 'required|array',
            'extras.availability' => 'nullable|integer',
            'extras.availability_days' => ['required', 'array', 'size:7'],
            'extras.availability_days.*' => 'nullable|integer',  // Ensure each day is either null or an integer
        ];
    }
}
