<?php

namespace App\Http\Requests\Admin;

use App\Rules\MobileNumberValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class UserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name' => ['required', 'string', 'max:50'],
            'email' => ['nullable', 'email:rfc,dns', 'max:50', 'unique:admins,email'],
            'username' => ['required', 'alpha_num', 'max:15', 'unique:admins,username'],
            'mobile' => ['required', 'numeric', 'gt:0', 'digits_between:8,13', 'unique:admins,mobile', new MobileNumberValidationRule()],
        ];
    }
}
