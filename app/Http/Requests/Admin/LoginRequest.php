<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        if (is_numeric(request()->username)) {
            return [
                'username' => 'required|exists:admins,mobile',
                'password' => ['required'],
            ];
        } else {
            return [
                'username' => 'required|exists:admins,username',
                'password' => ['required'],
            ];
        }
    }

    public function messages()
    {
        return [
            'username.exists' => 'Invalid Credentials',
        ];
    }
}
