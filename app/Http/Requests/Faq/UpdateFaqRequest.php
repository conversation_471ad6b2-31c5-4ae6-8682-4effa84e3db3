<?php

namespace App\Http\Requests\Faq;

use App\Models\Faq;
use Illuminate\Foundation\Http\FormRequest;

class UpdateFaqRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $faqs = Faq::findOrFail($this->route()->parameter('faq'))->id;

        return [
            'title' => 'required|max:255|unique:faqs,title,'.$faqs.',id,deleted_at,NULL',
            'content' => 'required|max:5000',
            'extras' => 'nullable',
            'faq_category_id' => 'required|exists:faq_categories,id',
            'video_ids' => 'nullable|array',
            'image_ids' => 'nullable|array',
            'video_url' => 'nullable|string|max:255',
        ];
    }
}
