<?php

namespace App\Http\Requests\Media;

use Illuminate\Foundation\Http\FormRequest;

class UploadAudioRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {

        return [
            'file' => 'required|file|mimes:wav,ogg,mp3',
            'audio_title' => 'required|max:255',
            'content_display_category_ids' => 'nullable|array',
            'content_display_category_ids.*' => 'exists:content_display_categories,id',
            'sound_by' => 'nullable',
            'script_by' => 'nullable',
        ];
    }
}
