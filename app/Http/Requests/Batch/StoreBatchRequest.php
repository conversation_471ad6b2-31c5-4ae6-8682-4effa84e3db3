<?php

namespace App\Http\Requests\Batch;

use Illuminate\Foundation\Http\FormRequest;

class StoreBatchRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [

            'batch_id' => 'required',
            'end_date' => 'required',
            'package_id' => 'required',
            'start_date' => 'required',
            'status' => 'required',
            'title' => 'required',
        ];
    }
}
