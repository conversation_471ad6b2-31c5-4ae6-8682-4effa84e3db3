<?php

namespace App\Http\Requests\Event;

use App\Enums\Event\SchoolEventStatusEnum;
use App\Enums\Event\SchoolEventTypeEnum;
use App\Models\SchoolEvent;
use App\Rules\MobileNumberValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Carbon\Carbon;

class StoreSchoolEventRequest extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }


    public function rules(): array
    {
        return [
            'title' => ['required',' min:2'],
            'description' => ['required', 'min:5'],
            'media_ids' => ['nullable', 'array'],
            'media_ids.*' => ['exists:media,id'],
            'venue_type' => ['required'],
            'venue_id' => ['required'],
            'type' => ['required', 'in:' . implode(',', SchoolEventTypeEnum::toArray())],
            'status' => ['required', 'in:' . implode(',', SchoolEventStatusEnum::toArray())],
            'event_date' => ['required', 'date', 'after:last month', fn ($attribute, $value, $fail) => $this->checkUniqueCollegeEvents($attribute, $value, $fail)],
            'members' => ['nullable', 'array'],
            'members.*' => ['exists:admins,id'],
            'other_members' => ['nullable', 'array'],
            'other_members.*.name' => ['required', 'min:2', 'max:255'],
            'other_members.*.contact_number' => ['nullable', 'numeric', 'unique:admins,mobile', new MobileNumberValidationRule()],
            'other_members.*.contact_email' => ['nullable', 'email'],
            'other_members.*.designation' => ['nullable', 'min:1'],
            'duration' => ['required', 'numeric', 'min:5'],
            'remark' => ['required_if:status,' . SchoolEventStatusEnum::Cancelled->value]
        ];
    }

    private function checkUniqueCollegeEvents(string $attribute, mixed $value, \Closure $fail): void
    {
        $parsedCurrentValue = Carbon::parse($value);

        $existingValueEvent = SchoolEvent::query()
            ->where('venue_type', $this->venue_type)
            ->where('venue_id', $this->venue_id)
            ->where('status', SchoolEventStatusEnum::Pending->value)
            ->whereDate($attribute, $parsedCurrentValue->format('Y-m-d'))
            ->get(['id', 'event_date', 'duration', 'duration_type']);

        foreach ($existingValueEvent as $existingEvent) {
            $addGenerator = ucfirst($existingEvent->duration_type);
            $endTime = $existingEvent->event_date->{"add$addGenerator"}($existingEvent->duration);

            if ($parsedCurrentValue->between($existingEvent->event_date, $endTime)) {
                $fail('The current timing conflicts with the planned event for the institution.');
            }
        }
    }
}
