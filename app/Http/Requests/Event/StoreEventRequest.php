<?php

namespace App\Http\Requests\Event;

use App\Models\EventCategory;
use App\Models\Media;
use Closure;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreEventRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255', 'unique:events,title'],
            'event_category_id' => ['required', 'exists:event_categories,id'],
            'date' => ['required', 'date_format:Y-m-d'],
            'description' => ['required', 'max:20000'],
            'media_id' => ['nullable', 'array'],
            'media_id.*' => ['required', 'exists:media,id'],
            'mobile_image_id' => ['required', 'exists:media,id'],
            'status' => ['nullable', 'in:0,1'],
            'tags' => ['nullable', 'array'],
            'tags.*' => ['required', 'max:255'],
            'college_id' => [
                'nullable',
                'exists:collages,id',
            ],
            'short_description' => 'nullable',
            'shift' => 'required|array',
            'location' => 'nullable',
            'map_iframe' => 'nullable',
            'terms_and_condition' => 'nullable',
            'starting_ref_id' => [
                'nullable',
                'numeric',
            ],
            'admit_card_template_id' => ['nullable', Rule::requiredIf(function () {
                if (request()->input('event_category_id')) {
                    $category = EventCategory::query()
                        ->where('id', request()->input('event_category_id'))->first();
                    return $category->slug == 'physical-exam';
                }
                return false;
            }), 'exists:media,id', function (string $attribute, mixed $value, Closure $fail) {
                if ($value) {
                    $media = Media::find($value);
                    if ($media->disk == 'agminiocdn') {
                        $fail("Admit card template image is corrupted. Choose different template image");
                    }
                }
            }],
            'program' => ['nullable', 'string', 'min:2'],
            'can_register' => ['nullable', 'boolean'],
            'end_date' => 'nullable|date',
            'is_paid' => 'required|boolean',
            'price' => 'required_if:is_paid,true',
        ];
    }

    public function messages()
    {
        return [
            'mobile_image_id.required' => 'Mobile image is required. ',
        ];
    }
}
