<?php

namespace App\Http\Requests\Event;

use App\Models\Event;
use Closure;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Carbon;

class StoreEventActivityRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'title' => [
                'required', 'max:255',
            ],
            'description' => ['required', 'max:100000'],
            'event_id' => [
                'required',
                'exists:events,id',
            ],
            'date_time' => [$this->dateTimeValidation()]
        ];
    }

    private function dateTimeValidation(): Closure
    {
        return function (string $attribute, mixed $value, Closure $fail) {
            $eventId = request()->input('event_id');
            $event = Event::query()
                ->where('id', $eventId)->select('date', 'end_date')->first();
            if ($event->end_date) {

                if ($value) {
                    $startDate = Carbon::parse($event->date);
                    $endDate = Carbon::parse($event->end_date);

                    $currentDate = Carbon::parse($value);

                    if ($currentDate->lessThan($startDate) || $currentDate->greaterThan($endDate)) {
                        $fail("The date should be in between start and end date of event.");

                    }

                } else {
                    $fail('Date field is required for multi-day event');
                }
            }
        };
    }

}
