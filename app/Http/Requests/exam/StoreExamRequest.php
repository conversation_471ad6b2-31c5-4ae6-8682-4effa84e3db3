<?php

namespace App\Http\Requests\exam;

use App\Enums\ExamTypeEnum;
use Carbon\Carbon;
use Closure;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;

class StoreExamRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $end_time = Carbon::create($this->start_time)->addMinutes($this->duration)->format('Y-m-d H:i:s');

        $rules = [
            'title' => 'required|max:255',
            'description' => 'nullable|max:5000',
            'year' => 'nullable|numeric',
            'status' => ['required', 'boolean'],
            'exam_setup_id' => 'nullable|exists:exam_setups,id',
            'start_time' => 'required_if:status,1|date|date_format:"Y-m-d H:i:s"',
//            'duration' => 'required_if:status,1|numeric|gte:0|lte:180',
            'marks' => 'nullable|numeric|gte:0|lte:500',
            'participant_limit' => [
                function ($attribute, $value, $fail) {
                    if (request()->input('status') == 1 && request()->input('type') != ExamTypeEnum::LAB_TEST->value) {
                        if (!is_numeric($value) || $value < 0 || strlen((string)$value) > 9) {
                            $fail('The ' . $attribute . ' must be a numeric value greater than or equal to 0 and between 1 and 9 digits.');
                        }
                    }
                },
            ],
            'remainder_datetime' => 'nullable|date|date_format:"Y-m-d H:i:s',
            'flexible_time' => 'nullable|date|date_format:"Y-m-d H:i:s',
            'terms_and_condition' => 'nullable|max:10000',
            'rules' => 'nullable|max:10000',
            'is_free' => 'nullable|boolean',
            'rewards' => 'nullable|array',
            'rewards.*.position' => 'required|numeric|gte:0|lte:6|distinct:strict',
            'rewards.*.prize' => 'required|numeric|gte:0|lte:1000000',
            'registration_deadline' => 'required_if:status,1|date|date_format:"Y-m-d H:i:s"|before_or_equal:start_time',
            'tags' => 'nullable|array',
            'tags.*' => 'max:255|required',
            'exam_reminders' => 'nullable|array',
            'exam_reminders.*' => [
                'required',
                'exists:remainder_templates,id',
            ],

            'banner_title' => 'nullable|max:255',
            'banner_description' => 'nullable|max:5000',
            'is_criteria' => 'nullable|boolean',
            'criteria_fields' => 'nullable|array',
            'criteria_fields.*' => 'max:255',
            'is_featured' => 'nullable|boolean',
            'is_published' => 'nullable|boolean',
            'published_at' => 'required_if:status,1|date|date_format:"Y-m-d H:i:s"',
            'media_id' => 'nullable|exists:media,id',
            'negative_marking' => 'nullable|numeric|gte:0|lte:100',
            'join_in_middle' => 'nullable|in:0,1',
            'exam_threshold_id' => 'required|exists:exam_thresholds,id',
            'questions' => 'nullable|array',
            'questions.*.id' => 'exists:questions,id',
            'questions.*.order' => 'numeric|required|gte:1|lte:1000',
            'questions.*.marks' => 'numeric|required|gte:1|lte:10',
            'confirmation_message' => 'nullable|max:1000',
            'pass_percentage' => [Rule::requiredIf(fn() => request()->input('type') != ExamTypeEnum::BOOK_PRACTICE->value && request()->input('type') != ExamTypeEnum::DPP->value),
                'numeric', 'gte:1', 'lte:100'],
            'number_of_participant' => 'nullable|numeric|gte:1|lte:500000',
            'packages' => 'nullable|array',
            'packages.*' => 'exists:packages,id',
            'is_routine' => 'nullable',
            'number_of_questions' => 'nullable',
            'type' => [
                'nullable',
                new Enum(ExamTypeEnum::class),
            ],
            'model_category_set_ids' => 'nullable|array',
            'model_category_set_ids.*' => 'exists:model_category_sets,id',
            'guru_org_id' => 'required_if:type,DPP',
        ];
        if ($this->type == 'HOMEWORK') {
            $rules['duration'] = 'nullable';
        } else {
            $rules['duration'] = 'required_if:status,1|numeric|gte:0|lte:180';
        }
        return $rules;
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'type' => $this['is_routine'] === true ? 'ROUTINE' : $this->input('type'),
        ]);
    }
}
