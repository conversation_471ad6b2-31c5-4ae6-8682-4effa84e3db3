<?php

namespace App\Http\Requests\Sms;

use Illuminate\Foundation\Http\FormRequest;

class SociairBulkSmsRequest extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }


    public function rules(): array
    {
        return [
            'customers' => ['nullable', 'array'],
            'customers.*' => ['exists:customers,id'],
            'message' => ['required', 'max:1000'],
            'filters' => ['nullable', 'array'],
        ];
    }
}
