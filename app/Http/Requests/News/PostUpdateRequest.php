<?php

namespace App\Http\Requests\News;

use Illuminate\Foundation\Http\FormRequest;

class PostUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'title' => 'nullable',
            'caption' => 'nullable',
            'description' => 'nullable',
            'user_type' => 'string',
            'user_id' => 'integer',
            'status' => 'boolean',
            'is_draft' => 'boolean',
            'is_reported' => 'boolean',
            'post_visibility' => 'string|nullable',
            'schedule_at' => 'string|nullable',
            'show_for' => 'nullable',
            'action_type' => 'nullable',
            'extras' => 'nullable|array',
            'is_pinned' => ['nullable', 'boolean'],
            'pinned_expiry' => ['nullable', 'date', 'after_or_equal:now']
        ];
    }
}
