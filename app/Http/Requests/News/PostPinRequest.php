<?php

namespace App\Http\Requests\News;

use Illuminate\Foundation\Http\FormRequest;

class PostPinRequest extends FormRequest
{

    public function authorize(): bool
    {
        return auth()->check();
    }


    public function rules(): array
    {
        return [
            'pin_until' => ['sometimes', 'required', 'date:Y-m-d H:i:s', 'after_or_equal:now']
        ];
    }

    public function attributes()
    {
        return [
            'pin_until' => 'pin post until date'
        ];
    }
}
