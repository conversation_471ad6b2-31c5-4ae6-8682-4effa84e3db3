<?php

namespace App\Http\Requests\Exam;

use App\Rules\SectionTypeRule;
use Illuminate\Foundation\Http\FormRequest;

class SyncExamQuestionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'questions' => 'required|array',
            'questions.*.id' => 'exists:questions,id',
            'questions.*.marks' => 'gte:0|lte:10',
            'questions.*.order' => 'gte:0|lte:1000',
            'questions.*.extras' => 'required|array',
            'questions.*.extras.subject_type' => 'required|in:Section,Subject',

            'questions.*.extras.subject' => 'required|array',
            'questions.*.extras.subject.id' => ['required', 'exists:sections,id', new SectionTypeRule(['Subject', 'Section'])],
            'questions.*.extras.subject.name' => ['required'],
            'questions.*.extras.subject.parent_id' => ['nullable'],
            'questions.*.extras.subject.type' => ['required', 'exists:sections,type'],

            'questions.*.extras.unit' => 'required_if:questions.*.subject_type,Subject|array',
            'questions.*.extras.unit.id' => ['required_if:subject_type,Subject', 'exists:sections,id', new SectionTypeRule('Unit')],
            'questions.*.extras.unit.name' => ['required_if:subject_type,Subject'],
            'questions.*.extras.unit.parent_id' => ['required_if:subject_type,Subject', 'exists:sections,parent_id'],
            'questions.*.extras.unit.type' => ['required_if:subject_type,Subject', 'exists:sections,type'],
            'questions.*.extras.unit.marks' => ['required_if:subject_type,Subject', 'exists:sections,marks'],

            'questions.*.extras.chapter' => 'required|array',
            'questions.*.extras.chapter.id' => ['required', 'exists:sections,id', new SectionTypeRule(['Chapter', 'Sub Section'])],
            'questions.*.extras.chapter.name' => ['required'],
            'questions.*.extras.chapter.parent_id' => ['required', 'exists:sections,parent_id'],
            'questions.*.extras.chapter.type' => ['required', 'exists:sections,type'],
            'questions.*.extras.chapter.marks' => ['required', 'exists:sections,marks'],
        ];
    }
}
