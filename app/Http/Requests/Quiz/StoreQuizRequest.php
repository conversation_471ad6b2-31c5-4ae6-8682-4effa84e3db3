<?php

namespace App\Http\Requests\Quiz;

use Illuminate\Foundation\Http\FormRequest;

class StoreQuizRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'question_id' => ['required', 'exists:questions,id'],
            'package_ids' => ['required', 'array'],
            'package_ids.*' => ['required', 'exists:packages,id'],
            'for_date' => [
                'date',
                'required',
            ],
            'reward_points' => ['nullable', 'integer'],
            'published_at' => [
                'date',
                'nullable',
                'date_format:"Y/m/d H:i:s"',
                'after_or_equal:for_date',
            ],

            'number_of_winner' => [
                'required',
                'integer',
                'gte:1',
                'lte:1000',
            ],
        ];
    }
}
