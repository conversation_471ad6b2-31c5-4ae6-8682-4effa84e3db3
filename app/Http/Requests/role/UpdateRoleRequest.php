<?php

namespace App\Http\Requests\role;

use App\Models\Role;
use Illuminate\Foundation\Http\FormRequest;

class UpdateRoleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $role = Role::findOrFail($this->route()->parameter('role'));

        return [
            'name' => 'required|max:50|unique:roles,name,'.$role->id,
            'display_name' => 'required|max:50',
            'guard_name' => 'required|max:50|in:admin-api',
            'permissions' => 'required|array',
            'permissions.*' => 'exists:permissions,id',
        ];
    }
}
